#define SDL_MAIN_USE_CALLBACKS 1
#include <SDL3/SDL.h>
#include <SDL3/SDL_main.h>
#include <SDL3_ttf/SDL_ttf.h>
#include "Button.h"

// Vertex structure for main pipeline (position and color)
typedef struct {
    float x, y, z;       // Position (3D for shader compatibility)
    Uint8 r, g, b, a;    // Color (normalized to 0-255)
} PositionColorVertex;

// Application context
typedef struct
{
	// Window and device
	SDL_Window *window;
	SDL_GPUDevice *device;
	int window_width;
	int window_height;
	float last_tick;
	float delta_time;
	bool app_in_background;
	bool running;
	bool window_resized;

	// GPU resources
	SDL_GPUGraphicsPipeline *pipeline;
	SDL_GPUCommandBuffer *cmd_buf;

	// Text rendering
	TTF_Font *font;
	TTF_TextEngine *text_engine;
	TTF_Text *button_text;
	TTF_GPUAtlasDrawSequence *draw_sequence;

	// Button
	Button button;
} Context;

// Error checking helpers
void check_error_bool(const bool res)
{
	if (!res)
	{
		SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "%s", SDL_GetError());
	}
}

void *check_error_ptr(void *ptr)
{
	if (!ptr)
	{
		SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "%s", SDL_GetError());
	}
	return ptr;
}


// Shader loading function
SDL_GPUShader *load_shader(SDL_GPUDevice *device, const char *filename, SDL_GPUShaderStage stage)
{
	char fullPath[256];
	SDL_snprintf(fullPath, sizeof(fullPath), "build/assets/Compiled/MSL/%s", filename);

	size_t codeSize;
	void *code = SDL_LoadFile(fullPath, &codeSize);
	if (code == NULL)
	{
		SDL_Log("Failed to load shader from disk! %s", fullPath);
		return NULL;
	}

	SDL_GPUShaderCreateInfo shaderInfo = {
			.code = code,
			.code_size = codeSize,
			.entrypoint = "main0", // MSL uses main0
			.format = SDL_GPU_SHADERFORMAT_MSL,
			.stage = stage,
			.num_samplers = 0,
			.num_uniform_buffers = 0,
			.num_storage_buffers = 0,
			.num_storage_textures = 0};

	SDL_GPUShader *shader = SDL_CreateGPUShader(device, &shaderInfo);
	if (shader == NULL)
	{
		SDL_Log("Failed to create shader!");
		SDL_free(code);
		return NULL;
	}

	SDL_free(code);
	return shader;
}

// Create graphics pipeline
int create_pipeline(Context *context)
{
	// Create the shaders
	int result = 0;
	SDL_GPUShader *vertexShader = NULL;
	SDL_GPUShader *fragmentShader = NULL;

	SDL_Log("Loading main vertex shader...");
	vertexShader = load_shader(
			context->device, "PositionColor.vert.msl", SDL_GPU_SHADERSTAGE_VERTEX);
	if (vertexShader == NULL)
	{
		SDL_Log("Failed to create vertex shader!");
		result = -1;
		goto cleanup;
	}
	SDL_Log("Main vertex shader loaded successfully");

	SDL_Log("Loading main fragment shader...");
	fragmentShader = load_shader(
			context->device, "SolidColor.frag.msl", SDL_GPU_SHADERSTAGE_FRAGMENT);
	if (fragmentShader == NULL)
	{
		SDL_Log("Failed to create fragment shader!");
		SDL_ReleaseGPUShader(context->device, vertexShader);
		result = -1;
		goto cleanup;
	}
	SDL_Log("Main fragment shader loaded successfully");

	// Create the pipeline
	SDL_GPUGraphicsPipelineCreateInfo pipelineCreateInfo = {
			.target_info = {
					.num_color_targets = 1,
					.color_target_descriptions = (SDL_GPUColorTargetDescription[]){
							{
									.format = SDL_GetGPUSwapchainTextureFormat(context->device, context->window),
									.blend_state = (SDL_GPUColorTargetBlendState){
											.enable_blend = true,
											.alpha_blend_op = SDL_GPU_BLENDOP_ADD,
											.color_blend_op = SDL_GPU_BLENDOP_ADD,
											.color_write_mask = 0xF,
											.src_alpha_blendfactor = SDL_GPU_BLENDFACTOR_SRC_ALPHA,
											.dst_alpha_blendfactor = SDL_GPU_BLENDFACTOR_DST_ALPHA,
											.src_color_blendfactor = SDL_GPU_BLENDFACTOR_SRC_ALPHA,
											.dst_color_blendfactor = SDL_GPU_BLENDFACTOR_ONE_MINUS_SRC_ALPHA //
									} //
							} //
					},
			},
			// This is set up to match the vertex shader layout!
			.vertex_input_state = (SDL_GPUVertexInputState){
					//
					.num_vertex_buffers = 1,
					.vertex_buffer_descriptions = (SDL_GPUVertexBufferDescription[]){{
							//
							.slot = 0,
							.input_rate = SDL_GPU_VERTEXINPUTRATE_VERTEX,
							.pitch = sizeof(PositionColorVertex) //
					}},																			 //
					.num_vertex_attributes = 2,
					.vertex_attributes = (SDL_GPUVertexAttribute[]){
							{
									.buffer_slot = 0,
									.format = SDL_GPU_VERTEXELEMENTFORMAT_FLOAT3, //
									.location = 0,
									.offset = 0 //
							},
							{
									.buffer_slot = 0,
									.format = SDL_GPU_VERTEXELEMENTFORMAT_UBYTE4_NORM, //
									.location = 1,
									.offset = sizeof(float) * 3 //
							} //
					} //
			},
			.primitive_type = SDL_GPU_PRIMITIVETYPE_TRIANGLELIST,
			.vertex_shader = vertexShader,
			.fragment_shader = fragmentShader //
	};

	SDL_Log("Creating main graphics pipeline...");
	context->pipeline = SDL_CreateGPUGraphicsPipeline(context->device, &pipelineCreateInfo);
	if (context->pipeline == NULL)
	{
		SDL_Log("Failed to create pipeline: %s", SDL_GetError());
		result = -1;
		goto cleanup;
	}
	SDL_Log("Main graphics pipeline created successfully");

cleanup:
	if (vertexShader)
	{
		SDL_ReleaseGPUShader(context->device, vertexShader);
	}
	if (fragmentShader)
	{
		SDL_ReleaseGPUShader(context->device, fragmentShader);
	}

	return result;
}

// Initialize button rendering resources
int create_buffers(Context *context)
{
	// Initialize button rendering resources
	SDL_Log("Initializing button rendering resources...");
	if (!Button_InitRenderResources(context->device, 1)) {
		SDL_Log("Failed to initialize button rendering resources!");
		return -1;
	}
	SDL_Log("Button rendering resources initialized successfully");

	return 0;
}

// Initialize button
void init_button(Context *context)
{
	// Position the button at (100, 100) from the bottom left
	float button_x = 100.0f;
	float button_y = context->window_height - 100.0f - 50.0f; // 50px height

	Button_Init(&context->button, button_x, button_y, 150.0f, 50.0f, "button");
}

// Initialize the application
int init(Context *context)
{
	// Initialize SDL
	if (!SDL_Init(SDL_INIT_VIDEO | SDL_INIT_EVENTS))
	{
		SDL_Log("SDL initialization failed: %s", SDL_GetError());
		return -1;
	}

	// Create window
	context->window_width = 800;
	context->window_height = 600;
	context->window = SDL_CreateWindow("UI Button Test", context->window_width, context->window_height, SDL_WINDOW_RESIZABLE);
	if (!context->window)
	{
		SDL_Log("Window creation failed: %s", SDL_GetError());
		return -1;
	}

	// Create GPU device
	context->device = SDL_CreateGPUDevice(
			SDL_GPU_SHADERFORMAT_SPIRV | SDL_GPU_SHADERFORMAT_DXIL | SDL_GPU_SHADERFORMAT_MSL,
			false, NULL);
	if (!context->device)
	{
		SDL_Log("GPU device creation failed: %s", SDL_GetError());
		return -1;
	}

	// Claim window for GPU device
	if (!SDL_ClaimWindowForGPUDevice(context->device, context->window))
	{
		SDL_Log("Failed to claim window for GPU device: %s", SDL_GetError());
		return -1;
	}

	// Create pipeline
	if (create_pipeline(context) < 0)
	{
		return -1;
	}

	// Create buffers
	if (create_buffers(context) < 0)
	{
		return -1;
	}

	// Initialize TTF for text rendering
	if (!TTF_Init())
	{
		SDL_Log("TTF initialization failed: %s", SDL_GetError());
		return -1;
	}

	// Load font
	context->font = TTF_OpenFont("assets/fonts/Poppins/Poppins-Regular.ttf", 16);
	if (!context->font)
	{
		SDL_Log("Failed to load font: %s", SDL_GetError());
		// Continue without font for now
	}

	// Create text engine
	if (context->font)
	{
		context->text_engine = TTF_CreateGPUTextEngine(context->device);
		if (!context->text_engine)
		{
			SDL_Log("Failed to create text engine: %s", SDL_GetError());
		}
	}

	// Initialize button
	init_button(context);

	// Create button text if we have font and text engine
	if (context->font && context->text_engine)
	{
		context->button_text = TTF_CreateText(context->text_engine, context->font, context->button.text, 0);
		if (!context->button_text)
		{
			SDL_Log("Failed to create button text: %s", SDL_GetError());
		}
	}

	context->running = true;
	context->app_in_background = false;
	context->window_resized = false;

	return 0;
}

// Handle window resize
int handle_window_resize(Context *context)
{
	if (!context->window_resized)
	{
		return 0;
	}

	SDL_Log("Window resized to %dx%d", context->window_width, context->window_height);

	// Recreate pipeline for new swapchain format
	if (context->pipeline)
	{
		SDL_ReleaseGPUGraphicsPipeline(context->device, context->pipeline);
		context->pipeline = NULL;
	}

	if (create_pipeline(context) < 0)
	{
		SDL_Log("Failed to recreate pipeline after window resize!");
		return -1;
	}

	// Update button position to maintain position from bottom left
	float button_x = 100.0f;
	float button_y = context->window_height - 100.0f - 50.0f; // 50px height
	Button_UpdatePosition(&context->button, button_x, button_y);

	context->window_resized = false;
	return 0;
}

// Update function
int update(Context *context)
{
	// Handle window resize if needed
	if (handle_window_resize(context) < 0)
	{
		return -1;
	}

	// Update text rendering if available
	if (context->button_text)
	{
		context->draw_sequence = TTF_GetGPUTextDrawData(context->button_text);
	}

	return 0;
}


// Render function
int render(Context *context)
{
	// Acquire command buffer
	context->cmd_buf = SDL_AcquireGPUCommandBuffer(context->device);
	if (!context->cmd_buf)
	{
		SDL_Log("Failed to acquire command buffer: %s", SDL_GetError());
		return -1;
	}

	// Acquire swapchain texture
	SDL_GPUTexture *swapchain_texture;
	if (!SDL_WaitAndAcquireGPUSwapchainTexture(context->cmd_buf, context->window, &swapchain_texture, NULL, NULL))
	{
		SDL_Log("Failed to acquire swapchain texture: %s", SDL_GetError());
		return -1;
	}

	if (swapchain_texture != NULL)
	{
		// Prepare button vertex data
		Button_PrepareVertexData(&context->button, 0, context->window_width, context->window_height);

		// Upload button geometry data
		Button_UploadGeometryData(context->cmd_buf);

		// Set button color uniform
		Button_SetColorUniform(context->cmd_buf, &context->button);

		// Begin render pass
		SDL_GPUColorTargetInfo color_target_info = {0};
		color_target_info.texture = swapchain_texture;
		color_target_info.clear_color = (SDL_FColor){0.2f, 0.2f, 0.2f, 1.0f}; // Dark gray background
		color_target_info.load_op = SDL_GPU_LOADOP_CLEAR;
		color_target_info.store_op = SDL_GPU_STOREOP_STORE;

		SDL_GPURenderPass *render_pass = SDL_BeginGPURenderPass(context->cmd_buf, &color_target_info, 1, NULL);

		// Draw buttons
		Button_Draw(render_pass, context->cmd_buf, context->pipeline, &context->button, 1);

		// Draw text if available
		// Note: For now, we'll skip text rendering to get the button working first
		// Text rendering with SDL3_ttf requires a more complex setup with vertex buffers
		// and texture atlases that we can implement later

		SDL_EndGPURenderPass(render_pass);
	}

	// Submit command buffer
	SDL_SubmitGPUCommandBuffer(context->cmd_buf);

	return 0;
}

// SDL app callbacks
SDL_AppResult SDL_AppInit(void **appstate, int argc, char *argv[])
{
	(void)argc; // Unused parameter
	(void)argv; // Unused parameter
	Context *context = SDL_calloc(1, sizeof(Context));
	if (!context)
	{
		SDL_Log("Failed to allocate context");
		return SDL_APP_FAILURE;
	}
	*appstate = context;

	if (init(context) < 0)
	{
		return SDL_APP_FAILURE;
	}

	return SDL_APP_CONTINUE;
}

SDL_AppResult SDL_AppEvent(void *appstate, SDL_Event *event)
{
	Context *context = (Context *)appstate;

	switch (event->type)
	{
	case SDL_EVENT_KEY_UP:
		if (event->key.key == SDLK_ESCAPE)
		{
			context->running = false;
		}
		break;

	case SDL_EVENT_QUIT:
		context->running = false;
		break;

	case SDL_EVENT_WILL_ENTER_BACKGROUND:
		context->app_in_background = true;
		break;

	case SDL_EVENT_DID_ENTER_FOREGROUND:
		context->app_in_background = false;
		break;

	case SDL_EVENT_WINDOW_RESIZED:
		context->window_width = event->window.data1;
		context->window_height = event->window.data2;
		context->window_resized = true;
		break;

	case SDL_EVENT_MOUSE_MOTION:
	{
		// Check if mouse is over button
		float mouse_x = event->motion.x;
		float mouse_y = event->motion.y;
		Button_HandleMouseMotion(&context->button, mouse_x, mouse_y);
		break;
	}

	case SDL_EVENT_MOUSE_BUTTON_DOWN:
	{
		// Check if button is clicked
		if (event->button.button == SDL_BUTTON_LEFT)
		{
			float mouse_x = event->button.x;
			float mouse_y = event->button.y;
			Button_HandleMouseButtonDown(&context->button, mouse_x, mouse_y, event->button.button);
		}
		break;
	}

	case SDL_EVENT_MOUSE_BUTTON_UP:
	{
		// Check if button is released
		if (event->button.button == SDL_BUTTON_LEFT)
		{
			float mouse_x = event->button.x;
			float mouse_y = event->button.y;
			if (Button_HandleMouseButtonUp(&context->button, mouse_x, mouse_y, event->button.button))
			{
				// Button clicked!
				SDL_Log("helloworld");
			}
		}
		break;
	}
	}

	return context->running ? SDL_APP_CONTINUE : SDL_APP_SUCCESS;
}

SDL_AppResult SDL_AppIterate(void *appstate)
{
	Context *context = (Context *)appstate;

	if (context->app_in_background)
	{
		SDL_Delay(100);
		return SDL_APP_CONTINUE;
	}

	// Calculate delta time
	float newTime = SDL_GetTicks() / 1000.0f;
	context->delta_time = newTime - context->last_tick;
	context->last_tick = newTime;

	// Update
	if (update(context) < 0)
	{
		return SDL_APP_FAILURE;
	}

	// Render
	if (render(context) < 0)
	{
		return SDL_APP_FAILURE;
	}

	return SDL_APP_CONTINUE;
}

void cleanup(Context *context)
{
	// Clean up text resources
	if (context->button_text)
	{
		TTF_DestroyText(context->button_text);
	}
	if (context->text_engine)
	{
		TTF_DestroyGPUTextEngine(context->text_engine);
	}
	if (context->font)
	{
		TTF_CloseFont(context->font);
	}
	TTF_Quit();

	// Clean up button rendering resources
	Button_CleanupRenderResources();

	// Clean up GPU resources
	if (context->pipeline)
	{
		SDL_ReleaseGPUGraphicsPipeline(context->device, context->pipeline);
	}

	// Clean up window and device
	SDL_ReleaseWindowFromGPUDevice(context->device, context->window);
	SDL_DestroyGPUDevice(context->device);
	SDL_DestroyWindow(context->window);
	SDL_Quit();
}

void SDL_AppQuit(void *appstate, SDL_AppResult result)
{
	(void)result; // Unused parameter
	Context *context = (Context *)appstate;
	if (context)
	{
		cleanup(context);
		SDL_free(context);
	}
}