#ifndef BUTTON_H
#define BUTTON_H

#include <SDL3/SDL.h>
#include <stdbool.h>

// Vertex structure for position only (color comes from uniform)
typedef struct {
    float x, y, z;       // Position (3D for shader compatibility)
} PositionVertex;

// Uniform buffer structure for button color
typedef struct {
    float r, g, b, a;    // Color (normalized to 0.0-1.0)
} ButtonColorUniform;

// Button structure
typedef struct {
    SDL_FRect rect;      // Rectangle in screen coordinates
    SDL_FColor color;    // Button color
    SDL_FColor hoverColor; // Color when mouse is over button
    SDL_FColor textColor; // Text color
    char* text;          // Button text
    bool isHovered;      // Is mouse hovering over button
    bool isPressed;      // Is button currently pressed
    bool isDirty;        // Flag indicating if button state has changed
} Button;

// Button rendering resources (shared by all buttons)
typedef struct {
    SDL_GPUDevice* device;
    SDL_GPUBuffer* vertex_buffer;
    SDL_GPUBuffer* index_buffer;
    SDL_GPUTransferBuffer* transfer_buffer;
    PositionVertex* vertices;
    Uint16* indices;
    int max_buttons;
    bool initialized;

    // Shader resources
    SDL_GPUShader* vertex_shader;
    SDL_GPUShader* fragment_shader;
    SDL_GPUGraphicsPipeline* pipeline;
} ButtonRenderResources;

// No push constants needed

// Initialize button rendering resources
bool Button_InitRenderResources(SDL_GPUDevice* device, int max_buttons);

// Clean up button rendering resources
void Button_CleanupRenderResources();

// Initialize a button with default properties
void Button_Init(Button* button, float x, float y, float width, float height, const char* text);

// Update button position (e.g., after window resize)
void Button_UpdatePosition(Button* button, float x, float y);

// Check if a point is inside the button
bool Button_ContainsPoint(Button* button, float x, float y);

// Handle mouse motion event
void Button_HandleMouseMotion(Button* button, float x, float y);

// Handle mouse button down event
bool Button_HandleMouseButtonDown(Button* button, float x, float y, Uint8 button_type);

// Handle mouse button up event
bool Button_HandleMouseButtonUp(Button* button, float x, float y, Uint8 button_type);

// Get the current color of the button (based on hover state)
SDL_FColor Button_GetCurrentColor(Button* button);

// Mark the button as dirty (needing visual update)
void Button_MarkDirty(Button* button);

// Prepare button vertex data for rendering
void Button_PrepareVertexData(Button* button, int button_index, int screen_width, int screen_height);

// Upload button geometry data to GPU
void Button_UploadGeometryData(SDL_GPUCommandBuffer* cmd_buf);

// Draw buttons using the provided render pass and command buffer
void Button_Draw(SDL_GPURenderPass* render_pass, SDL_GPUCommandBuffer* cmd_buf, SDL_GPUGraphicsPipeline* pipeline, Button* buttons, int button_count);

// Set button color uniform
void Button_SetColorUniform(SDL_GPUCommandBuffer* cmd_buf, Button* button);

#endif // BUTTON_H